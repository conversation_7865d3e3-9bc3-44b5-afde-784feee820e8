﻿2025-09-28 17:17:23,498 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:17:23,499 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:17:23,507 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:17:24,495 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:17:24,496 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:17:24,522 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:17:24,523 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:17:24,538 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:17:24,541 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:17:24,549 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:17:24,549 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:17:24,604 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:17:24,604 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:17:28,935 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:17:28,941 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:17:28,947 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:17:28,948 - [BaseRepository] 参数数量: 5
2025-09-28 17:17:29,052 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:21:14,079 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:21:14,079 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:21:14,084 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:21:14,979 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:21:14,980 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:21:14,996 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:21:14,997 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:21:15,006 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:21:15,008 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:21:15,016 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:21:15,016 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:21:15,040 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:21:15,040 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:21:18,943 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:21:18,948 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:21:18,954 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:21:18,954 - [BaseRepository] 参数数量: 5
2025-09-28 17:21:19,022 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:22:06,110 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:22:06,110 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:22:06,120 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:22:07,053 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:22:07,053 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:22:07,071 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:22:07,071 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:22:07,081 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:22:07,083 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:22:07,091 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:22:07,091 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:22:07,116 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:22:07,116 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:23:23,193 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:23:23,194 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:23:23,200 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:23:24,186 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:23:24,187 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:23:24,205 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:23:24,205 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:23:24,215 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:23:24,218 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:23:24,228 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:23:24,229 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:23:24,255 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:23:24,256 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:23:50,365 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:23:50,370 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:23:50,376 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:23:50,376 - [BaseRepository] 参数数量: 5
2025-09-28 17:23:50,425 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:24:31,764 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:24:31,765 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:24:31,773 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:24:32,787 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:24:32,789 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:24:32,809 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:24:32,809 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:24:32,820 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:24:32,822 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:24:32,833 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:24:32,834 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:24:32,858 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:24:32,858 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:25:50,340 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:25:50,348 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:25:50,355 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:25:50,355 - [BaseRepository] 参数数量: 5
2025-09-28 17:25:50,431 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:27:31,675 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:27:31,675 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:27:31,680 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:27:32,569 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:27:32,570 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:27:32,587 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:27:32,588 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:27:32,598 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:27:32,600 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:27:32,608 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:27:32,609 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:27:32,632 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:27:32,633 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:27:41,893 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:27:41,898 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:27:41,904 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:27:41,905 - [BaseRepository] 参数数量: 5
2025-09-28 17:27:41,954 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:27:47,531 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:27:47,532 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:27:47,533 - [BaseRepository] 参数数量: 5
2025-09-28 17:27:47,570 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:29:14,468 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:29:14,469 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:29:14,474 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:29:15,343 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:29:15,344 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:29:15,360 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:29:15,361 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:29:15,370 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:29:15,372 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:29:15,380 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:29:15,380 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:29:15,406 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:29:15,406 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:29:18,926 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:29:18,932 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:29:18,938 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:29:18,939 - [BaseRepository] 参数数量: 5
2025-09-28 17:29:18,987 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:30:19,982 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:30:19,983 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:19,989 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:20,892 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:20,892 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:20,914 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:30:20,914 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:30:20,928 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:30:20,930 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:30:20,937 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:20,938 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:20,963 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:20,963 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:25,036 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:30:25,041 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:30:25,047 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:30:25,048 - [BaseRepository] 参数数量: 5
2025-09-28 17:30:25,191 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:30:58,710 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:30:58,711 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:58,718 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:59,647 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:59,647 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:59,669 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:30:59,670 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:30:59,681 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:30:59,683 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:30:59,692 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:59,692 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:30:59,720 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:30:59,721 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:31:58,045 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:31:58,046 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:31:58,050 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:31:58,961 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:31:58,962 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:31:58,979 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:31:58,979 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:31:58,990 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:31:58,992 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:31:58,999 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:31:59,000 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:31:59,025 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:31:59,026 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:32:02,556 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:32:02,561 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:32:02,567 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:32:02,567 - [BaseRepository] 参数数量: 5
2025-09-28 17:32:02,622 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:33:10,827 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:33:10,827 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:33:10,833 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:33:11,765 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:33:11,766 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:33:11,782 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:33:11,782 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:33:11,795 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:33:11,797 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:33:11,805 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:33:11,806 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:33:11,830 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:33:11,830 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:33:15,942 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:33:15,947 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:33:15,953 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:33:15,954 - [BaseRepository] 参数数量: 5
2025-09-28 17:33:16,016 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:37:17,021 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:37:17,021 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:37:17,027 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:37:17,924 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:37:17,925 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:37:17,942 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:37:17,943 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:37:17,953 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:37:17,954 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:37:17,961 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:37:17,962 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:37:17,986 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:37:17,987 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:37:21,268 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:37:21,273 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:37:21,279 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:37:21,280 - [BaseRepository] 参数数量: 5
2025-09-28 17:37:21,327 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:37:49,252 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:37:49,253 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:45:16,870 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:45:16,872 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:16,878 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:17,851 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:17,852 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:17,869 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:45:17,870 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:45:17,880 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:45:17,882 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:45:17,889 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:17,889 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:17,913 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:17,913 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:21,602 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:45:21,607 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:45:33,427 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:45:33,432 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:45:33,432 - [BaseRepository] 参数数量: 5
2025-09-28 17:45:33,484 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:45:46,527 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:45:46,527 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:46,532 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:47,378 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:47,379 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:47,395 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:45:47,395 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:45:47,407 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:45:47,408 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:45:47,419 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:47,419 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:47,445 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:45:47,445 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:45:51,791 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:45:51,798 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:45:51,803 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:45:51,804 - [BaseRepository] 参数数量: 5
2025-09-28 17:45:51,842 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:48:38,308 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:48:38,308 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:54:37,313 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:54:37,313 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:54:37,319 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:54:38,762 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:54:38,763 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:54:38,781 - [DatabaseConnection] 连接池已初始化
2025-09-28 17:54:38,782 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:54:38,792 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-28 17:54:38,794 - [FrmLogMsg] 窗体初始化完成
2025-09-28 17:54:38,802 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:54:38,803 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:54:38,829 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-28 17:54:38,829 - [DbContext] 数据库上下文初始化完成
2025-09-28 17:54:42,851 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:54:42,857 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:54:42,864 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:54:42,865 - [BaseRepository] 参数数量: 5
2025-09-28 17:54:42,912 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:54:50,596 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:54:50,597 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:55:08,213 - [DatabaseConnection] 执行查询成功，返回 1 行数据
2025-09-28 17:55:08,259 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:55:11,211 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:55:12,475 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:55:12,476 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:55:12,476 - [BaseRepository] 参数数量: 5
2025-09-28 17:55:12,517 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:55:31,949 - [DatabaseConnection] 执行查询成功，返回 1 行数据
2025-09-28 17:55:32,167 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:55:32,896 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:55:33,715 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:55:33,715 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:55:33,716 - [BaseRepository] 参数数量: 5
2025-09-28 17:55:33,774 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:57:51,724 - [DatabaseConnection] 执行查询失败: Fatal error encountered during command execution, SQL: SELECT * FROM th_usermsg WHERE ID = @id
2025-09-28 17:57:51,751 - [BaseRepository] 根据ID查询实体失败: Fatal error encountered during command execution
2025-09-28 17:57:53,650 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:57:53,664 - [DatabaseConnection] 执行查询成功，返回 1 行数据
2025-09-28 17:57:53,753 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-28 17:57:53,754 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:59:02,453 - [DatabaseConnection] 执行查询失败: Fatal error encountered during command execution, SQL: SELECT * FROM th_usermsg
2025-09-28 17:59:02,484 - [BaseRepository] 查询所有实体失败: Fatal error encountered during command execution
2025-09-28 17:59:10,139 - [DatabaseConnection] 数据库连接已打开
2025-09-28 17:59:10,140 - [DatabaseConnection] 执行查询成功，返回 3 行数据
2025-09-28 17:59:10,141 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-28 17:59:10,141 - [BaseRepository] 参数数量: 5
2025-09-28 17:59:10,221 - [DatabaseConnection] 执行命令成功，影响 1 行
