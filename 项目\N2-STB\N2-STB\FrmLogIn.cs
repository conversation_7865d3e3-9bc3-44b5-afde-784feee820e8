using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using N2_STB.Services;
using N2_STB.Controls;
using Proj.DB.Models;

namespace N2_STB
{
    public partial class FrmLogIn : Form
    {
        private UserService userService;
        private Form1 form1;
        public string userText;
        public ThUserMsg? currentUser;
        public FrmLogIn()
        {
            InitializeComponent();

            // 🎯 应用统一UI样式
            ApplyUIStyle();

            // 加载历史用户
            LoadHistoryUsers();

            // 绑定用户选择和文本变化事件
            cbxUser.SelectedIndexChanged += CbxUser_SelectedIndexChanged;
            cbxUser.TextChanged += CbxUser_TextChanged;
        }

        public FrmLogIn(Form1 form)
        {
            InitializeComponent();
            form1 = form;

            ControlBox = false;
            AcceptButton = btnlogin;
            CancelButton = btnExit;

            // 获取连接字符串并初始化用户服务
            string connStr = ConfigurationManager.ConnectionStrings["connStr2"]?.ConnectionString ?? "";
            userService = new UserService(connStr);

            // 🎯 应用统一UI样式
            ApplyUIStyle();

            // 加载历史用户
            LoadHistoryUsers();

            // 绑定用户选择和文本变化事件
            cbxUser.SelectedIndexChanged += CbxUser_SelectedIndexChanged;
            cbxUser.TextChanged += CbxUser_TextChanged;
        }


        private async void btnlogin_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用登录按钮防止重复点击
                btnlogin.Enabled = false;
                btnlogin.Text = "登录中...";

                // 验证输入
                if (string.IsNullOrWhiteSpace(cbxUser.Text))
                {
                    MessageBox.Show("请输入用户名", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("请输入密码", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 执行用户认证
                var loginResult = await userService.AuthenticateUserAsync(cbxUser.Text.Trim(), txtPassword.Text);

                if (loginResult.Success && loginResult.User != null)
                {
                    // 登录成功
                    currentUser = loginResult.User;

                    // 设置当前用户权限
                    SetCurrentUserPermissions(currentUser);

                    // 记录登录历史
                    await userService.LogUserActivityAsync(currentUser, "Login");

                    // 设置用户文本（保持原有的格式化逻辑）
                    userText = cbxUser.Text;

                    // 保存用户到历史记录
                    SaveUserToHistory(cbxUser.Text.Trim());

                    // 更新权限等级显示
                    string level = GetPermissionLevelText(currentUser.Remarks ?? "");
                    txtLevel.Text = level;

                    // 隐藏登录窗口并显示主界面
                    this.Hide();
                    form1.ShowMainUI();
                    form1.ShowUserText(cbxUser.Text);
                    form1.portObject.InitForm();
                }
                else
                {
                    // 登录失败
                    MessageBox.Show(loginResult.Message, "登录失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Clear();
                    txtPassword.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"登录过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复登录按钮状态
                btnlogin.Enabled = true;
                btnlogin.Text = "Login";
            }
        }

        private async void btnExit_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果有当前用户，记录登出历史
                if (currentUser != null)
                {
                    await userService.LogUserActivityAsync(currentUser, "Logout");

                    // 清除当前用户权限
                    CurrentUserPermissions.ClearCurrentUserPermissions();

                    currentUser = null;
                }
            }
            catch (Exception)
            {
                // 记录登出历史失败不应该阻止退出
            }
            finally
            {
                //this.Hide();
                form1.Close();
            }
        }

        /// <summary>
        /// 获取当前登录用户
        /// </summary>
        /// <returns>当前用户信息</returns>
        public ThUserMsg? GetCurrentUser()
        {
            return currentUser;
        }

        /// <summary>
        /// 获取当前用户权限字符串
        /// </summary>
        /// <returns>权限字符串</returns>
        public string GetCurrentUserPermissions()
        {
            return currentUser?.Remarks ?? "";  // ThUserMsg中Remarks字段存储权限
        }

        /// <summary>
        /// 获取当前用户权限描述
        /// </summary>
        /// <returns>权限描述</returns>
        public string GetCurrentUserPermissionDescription()
        {
            try
            {
                string permissionString = GetCurrentUserPermissions();
                if (string.IsNullOrEmpty(permissionString))
                {
                    return "所有权限都允许（默认权限）";
                }

                var permissions = new UserPermissions(permissionString);
                return permissions.GetPermissionDescription();
            }
            catch (Exception)
            {
                return "权限解析失败";
            }
        }

        /// <summary>
        /// 用户登出
        /// </summary>
        public async Task LogoutAsync()
        {
            try
            {
                if (currentUser != null)
                {
                    await userService.LogUserActivityAsync(currentUser, "Logout");

                    // 清除当前用户权限
                    CurrentUserPermissions.ClearCurrentUserPermissions();

                    currentUser = null;
                }
            }
            catch (Exception)
            {
                // 记录登出历史失败不应该阻止登出
            }
        }

        /// <summary>
        /// 设置当前用户权限
        /// </summary>
        /// <param name="user">用户信息</param>
        private void SetCurrentUserPermissions(ThUserMsg user)
        {
            try
            {
                // 从用户的Remarks字段获取权限字符串 (ThUserMsg中Remarks存储权限)
                string permissionString = user.Remarks ?? "";

                // 设置当前用户权限
                CurrentUserPermissions.SetCurrentUserPermissions(permissionString);

                // 记录权限设置日志
                Console.WriteLine($"用户 {user.Name} ({user.ID}) 登录成功");
                Console.WriteLine($"权限字符串: '{permissionString}'");

                if (!string.IsNullOrEmpty(permissionString))
                {
                    var permissions = new UserPermissions(permissionString);
                    Console.WriteLine($"权限解析: {permissions.GetPermissionDescription()}");
                    Console.WriteLine($"  - 可以修改System: {permissions.CanAccessSystem()}");
                    Console.WriteLine($"  - 可以修改Recipe: {permissions.CanModifyRecipe()}");
                    Console.WriteLine($"  - 可以修改Online: {permissions.CanModifyOnline()}");

                    if(permissions.CanAccessSystem())
                    {
                        form1.isOperation = true; // 如果有System权限，则允许操作
                    }
                    else
                    {
                        form1.isOperation = false; // 没有System权限，则禁止操作
                    }
                }
                else
                {
                    Console.WriteLine("权限字符串为空，使用默认权限（所有权限都允许）");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置用户权限时发生错误: {ex.Message}");
                // 出错时设置默认权限（所有权限都允许）
                CurrentUserPermissions.ClearCurrentUserPermissions();
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        //protected override void Dispose(bool disposing)
        //{
        //    if (disposing)
        //    {
        //        userService?.Dispose();
        //    }
        //    base.Dispose(disposing);
        //}

        /// <summary>
        /// 应用UI样式 - 统一界面风格
        /// </summary>
        private void ApplyUIStyle()
        {
            try
            {
                // 应用整体样式到所有控件
                UIStyleManager.ApplyStyleToAllControls(this);
            }
            catch (Exception ex)
            {
                // 记录异常但不影响窗体正常使用
                Console.WriteLine($"应用FrmLogIn UI样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取用户历史记录文件路径
        /// </summary>
        private string GetUserHistoryFilePath()
        {
            string exePath = Application.ExecutablePath;
            string exeDir = Path.GetDirectoryName(exePath);
            return Path.Combine(exeDir, "UserHistory.txt");
        }

        /// <summary>
        /// 加载历史用户到下拉框
        /// </summary>
        private void LoadHistoryUsers()
        {
            try
            {
                string filePath = GetUserHistoryFilePath();
                if (File.Exists(filePath))
                {
                    string[] users = File.ReadAllLines(filePath);
                    cbxUser.Items.Clear();

                    // 去重并添加到下拉框
                    var uniqueUsers = users.Where(u => !string.IsNullOrWhiteSpace(u))
                                          .Distinct()
                                          .ToArray();

                    cbxUser.Items.AddRange(uniqueUsers);

                    // 如果有历史用户，默认选择第一个
                    //if (cbxUser.Items.Count > 0)
                    //{
                    //    cbxUser.SelectedIndex = 0;
                    //}
                }
            }
            catch (Exception ex)
            {
                // 加载历史用户失败不应该影响登录功能
                Console.WriteLine($"加载历史用户失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 用户选择变化事件
        /// </summary>
        private async void CbxUser_SelectedIndexChanged(object sender, EventArgs e)
        {
            await UpdateUserLevelFromComboBox();
        }

        /// <summary>
        /// 下拉框文本变化事件
        /// </summary>
        private async void CbxUser_TextChanged(object sender, EventArgs e)
        {
            await UpdateUserLevelFromComboBox();
        }

        /// <summary>
        /// 从下拉框更新用户权限等级
        /// </summary>
        private async Task UpdateUserLevelFromComboBox()
        {
            if (userService != null && !string.IsNullOrEmpty(cbxUser.Text))
            {
                string currentText = cbxUser.Text.Trim();
                await UpdateUserLevel(currentText);
            }
            else
            {
                txtLevel.Text = "";
            }
        }

        /// <summary>
        /// 更新用户权限等级显示
        /// </summary>
        /// <param name="username">用户名</param>
        private async Task UpdateUserLevel(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    txtLevel.Text = "";
                    return;
                }

                // 查询所有用户并找到匹配的用户
                var allUsers = await userService.GetAllUsersAsync();

                // 精确匹配用户名
                var user = allUsers.FirstOrDefault(u => u.Name.Equals(username, StringComparison.OrdinalIgnoreCase));

                if (user != null)
                {
                    // 解析权限字符串并显示等级
                    string permissionString = user.Remarks ?? "";
                    string level = GetPermissionLevelText(permissionString);
                    txtLevel.Text = level;

                    // 设置文本颜色以区分已知用户和未知用户
                    txtLevel.ForeColor = Color.Green;
                }
                else
                {
                    // 检查是否是部分匹配（用户正在输入）
                    var partialMatch = allUsers.FirstOrDefault(u => u.Name.StartsWith(username, StringComparison.OrdinalIgnoreCase));
                    if (partialMatch != null)
                    {
                        txtLevel.Text = "输入中...";
                        txtLevel.ForeColor = Color.Orange;
                    }
                    else
                    {
                        txtLevel.Text = "未知用户";
                        txtLevel.ForeColor = Color.Red;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新用户等级时发生错误: {ex.Message}");
                txtLevel.Text = "错误";
                txtLevel.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 根据权限字符串获取权限等级文本
        /// </summary>
        /// <param name="permissionString">权限字符串</param>
        /// <returns>权限等级文本</returns>
        private string GetPermissionLevelText(string permissionString)
        {
            if (string.IsNullOrEmpty(permissionString))
            {
                return "低权限"; // 默认为低权限
            }

            try
            {
                var permissions = new UserPermissions(permissionString);

                // 根据权限组合判断等级
                if (permissions.SystemAllowed && permissions.RecipeAllowed && permissions.OnlineAllowed)
                {
                    return "高权限";
                }
                else if (!permissions.SystemAllowed && permissions.RecipeAllowed && permissions.OnlineAllowed)
                {
                    return "中权限";
                }
                else
                {
                    return "低权限";
                }
            }
            catch (Exception)
            {
                return "低权限"; // 解析失败时默认为低权限
            }
        }

        /// <summary>
        /// 保存用户到历史记录
        /// </summary>
        /// <param name="username">用户名</param>
        private void SaveUserToHistory(string username)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username))
                    return;

                string filePath = GetUserHistoryFilePath();
                List<string> users = new List<string>();

                // 读取现有用户
                if (File.Exists(filePath))
                {
                    users = File.ReadAllLines(filePath).ToList();
                }

                // 如果用户已存在，先移除（这样可以将其移到最前面）
                users.RemoveAll(u => string.Equals(u, username, StringComparison.OrdinalIgnoreCase));

                // 将当前用户添加到最前面
                users.Insert(0, username);

                // 限制历史记录数量（最多保存10个用户）
                if (users.Count > 10)
                {
                    users = users.Take(10).ToList();
                }

                // 写入文件
                File.WriteAllLines(filePath, users);
            }
            catch (Exception ex)
            {
                // 保存历史用户失败不应该影响登录功能
                Console.WriteLine($"保存历史用户失败: {ex.Message}");
            }
        }
    }
}
