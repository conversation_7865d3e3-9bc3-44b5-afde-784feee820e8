﻿2025-09-29 13:01:38,363 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:01:38,364 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:01:38,369 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:01:39,305 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:01:39,305 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:01:39,322 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:01:39,323 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:01:39,333 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:01:39,335 - [FrmLogMsg] 窗体初始化完成
2025-09-29 13:01:39,345 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:01:39,346 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:01:39,373 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:01:39,374 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:01:42,673 - [DatabaseConnection] 数据库连接已打开
2025-09-29 13:01:42,678 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:01:42,806 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:01:45,704 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:01:45,708 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-29 13:01:45,709 - [BaseRepository] 参数数量: 5
2025-09-29 13:01:45,870 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-29 13:01:58,379 - [DatabaseConnection] 数据库连接已关闭
2025-09-29 13:01:58,380 - [DbContext] 数据库上下文已释放
2025-09-29 13:02:00,043 - [FrmLogMsg] 窗体加载完成
2025-09-29 13:15:55,165 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:15:55,165 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:15:55,171 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:15:56,212 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:15:56,213 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:15:56,239 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:15:56,240 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:15:56,253 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:15:56,254 - [FrmLogMsg] 窗体初始化完成
2025-09-29 13:15:56,266 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:15:56,267 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:15:56,303 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:15:56,303 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:15:57,833 - [DatabaseConnection] 数据库连接已打开
2025-09-29 13:15:57,839 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:15:57,988 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:16:00,615 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:16:00,631 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-29 13:16:00,632 - [BaseRepository] 参数数量: 5
2025-09-29 13:16:00,700 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-29 13:16:15,387 - [DatabaseConnection] 数据库连接已关闭
2025-09-29 13:16:15,387 - [DbContext] 数据库上下文已释放
2025-09-29 13:17:09,836 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:17:09,836 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:17:09,841 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:17:10,966 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:17:10,966 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:17:10,984 - [DatabaseConnection] 连接池已初始化
2025-09-29 13:17:10,984 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:17:10,997 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True;
2025-09-29 13:17:10,999 - [FrmLogMsg] 窗体初始化完成
2025-09-29 13:17:11,009 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:17:11,009 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:17:11,045 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:17:11,045 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:17:12,114 - [DatabaseConnection] 数据库连接已打开
2025-09-29 13:17:12,123 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:17:12,301 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:17:14,406 - [DatabaseConnection] 执行查询成功，返回 4 行数据
2025-09-29 13:17:14,411 - [BaseRepository] 执行插入SQL: INSERT INTO th_user (Type, User_ID, User_Name, Role_ID, Time) VALUES (@Type, @UserId, @UserName, @RoleId, @Time)
2025-09-29 13:17:14,411 - [BaseRepository] 参数数量: 5
2025-09-29 13:17:14,469 - [DatabaseConnection] 执行命令成功，影响 1 行
2025-09-29 13:17:50,115 - [DatabaseConnection] 数据库连接已关闭
2025-09-29 13:17:50,115 - [DbContext] 数据库上下文已释放
2025-09-29 13:17:54,615 - [DatabaseConnection] 执行查询失败: Cannot access a disposed object.
Object name: 'DatabaseConnection'., SQL: SELECT * FROM stkc.purgesetting
2025-09-29 13:17:54,652 - [BaseRepository] 查询所有实体失败: Cannot access a disposed object.
Object name: 'DatabaseConnection'.
2025-09-29 13:18:03,973 - [DatabaseConnection] 执行查询失败: Cannot access a disposed object.
Object name: 'DatabaseConnection'., SQL: SELECT * FROM stkc.purgesetting
2025-09-29 13:18:04,007 - [BaseRepository] 查询所有实体失败: Cannot access a disposed object.
Object name: 'DatabaseConnection'.
2025-09-29 13:18:22,516 - [DatabaseConnection] 初始化数据库连接: Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True;SslMode=None;AllowPublicKeyRetrieval=True
2025-09-29 13:18:22,516 - [DbContext] 数据库上下文初始化完成
2025-09-29 13:18:22,531 - [DatabaseConnection] 数据库连接已打开
2025-09-29 13:18:22,535 - [DatabaseConnection] 执行查询成功，返回 1 行数据
